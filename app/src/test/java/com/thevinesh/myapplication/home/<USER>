package com.thevinesh.myapplication.home

import com.thevinesh.myapplication.data.repository.HomeRepository
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.mockito.Mock
import org.mockito.MockitoAnnotations

class HomeVmTest {

    @Mock
    private lateinit var homeRepository: HomeRepository

    private lateinit var homeVm: HomeVm

    @Before
    fun setup() {
        MockitoAnnotations.openMocks(this)
        homeVm = HomeVm(homeRepository)
    }

    @Test
    fun `onQueryChanged should update query state`() {
        val testQuery = "test query"

        homeVm.onQueryChanged(testQuery)

        assertEquals(testQuery, homeVm.query.value)
    }

    @Test
    fun `HomeVm should be created successfully`() {
        assertNotNull("HomeVm should not be null", homeVm)
        assertEquals("Initial query should be empty", "", homeVm.query.value)
    }
}
