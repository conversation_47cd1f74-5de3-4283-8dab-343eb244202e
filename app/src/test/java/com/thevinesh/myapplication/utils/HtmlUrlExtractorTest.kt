package com.thevinesh.myapplication.utils

import org.junit.Assert.*
import org.junit.Test

class HtmlUrlExtractorTest {

    @Test
    fun `extractUrls should extract URLs from href attributes`() {
        val html = """
            <html>
                <body>
                    <a href="https://www.example.com">Example</a>
                    <a href="https://github.com/user/repo">GitHub</a>
                    <a href="http://stackoverflow.com/questions/123">Stack Overflow</a>
                </body>
            </html>
        """.trimIndent()

        val urls = HtmlUrlExtractor.extractUrls(html)

        assertEquals(3, urls.size)
        assertTrue(urls.contains("https://www.example.com"))
        assertTrue(urls.contains("https://github.com/user/repo"))
        assertTrue(urls.contains("http://stackoverflow.com/questions/123"))
    }

    @Test
    fun `extractUrls should extract URLs from data-href attributes`() {
        val html = """
            <div data-href="https://www.google.com/search?q=test">Search Result</div>
            <span data-href="https://news.ycombinator.com">Hacker News</span>
        """.trimIndent()

        val urls = HtmlUrlExtractor.extractUrls(html)

        assertEquals(2, urls.size)
        assertTrue(urls.contains("https://www.google.com/search?q=test"))
        assertTrue(urls.contains("https://news.ycombinator.com"))
    }

    @Test
    fun `extractUrls should extract direct HTTP URLs from text`() {
        val html = """
            <p>Visit https://www.example.com for more info</p>
            <div>Check out http://github.com/user/repo</div>
        """.trimIndent()

        val urls = HtmlUrlExtractor.extractUrls(html)

        assertTrue(urls.contains("https://www.example.com"))
        assertTrue(urls.contains("http://github.com/user/repo"))
    }

    @Test
    fun `extractUrls should filter out invalid URLs`() {
        val html = """
            <a href="javascript:void(0)">Invalid</a>
            <a href="mailto:<EMAIL>">Email</a>
            <a href="#section">Fragment</a>
            <a href="/relative/path">Relative</a>
            <a href="https://example.com/file.pdf">PDF File</a>
            <a href="https://example.com/image.jpg">Image</a>
            <a href="https://valid-site.com">Valid</a>
        """.trimIndent()

        val urls = HtmlUrlExtractor.extractUrls(html)

        // Should only contain the valid webpage URL
        assertEquals(1, urls.size)
        assertTrue(urls.contains("https://valid-site.com"))
    }

    @Test
    fun `extractUrls should clean URLs by removing fragments and trailing punctuation`() {
        val html = """
            <a href="https://example.com/page#section">Link with fragment</a>
            <a href="https://github.com/user/repo.">Link with trailing dot</a>
            <a href="https://stackoverflow.com/questions/123,">Link with comma</a>
        """.trimIndent()

        val urls = HtmlUrlExtractor.extractUrls(html)

        assertEquals(3, urls.size)
        assertTrue(urls.contains("https://example.com/page"))
        assertTrue(urls.contains("https://github.com/user/repo"))
        assertTrue(urls.contains("https://stackoverflow.com/questions/123"))
    }

    @Test
    fun `extractUrls should handle mixed case attributes`() {
        val html = """
            <a HREF="https://example.com">Uppercase</a>
            <a Href="https://github.com">Mixed case</a>
            <div DATA-HREF="https://stackoverflow.com">Data href uppercase</div>
        """.trimIndent()

        val urls = HtmlUrlExtractor.extractUrls(html)

        assertEquals(3, urls.size)
        assertTrue(urls.contains("https://example.com"))
        assertTrue(urls.contains("https://github.com"))
        assertTrue(urls.contains("https://stackoverflow.com"))
    }

    @Test
    fun `extractUrlsByDomain should group URLs by domain`() {
        val html = """
            <a href="https://www.github.com/user1/repo1">GitHub 1</a>
            <a href="https://github.com/user2/repo2">GitHub 2</a>
            <a href="https://stackoverflow.com/questions/1">SO 1</a>
            <a href="https://www.stackoverflow.com/questions/2">SO 2</a>
            <a href="https://example.com">Example</a>
        """.trimIndent()

        val urlsByDomain = HtmlUrlExtractor.extractUrlsByDomain(html)

        assertEquals(3, urlsByDomain.keys.size)
        assertTrue(urlsByDomain.containsKey("github.com"))
        assertTrue(urlsByDomain.containsKey("stackoverflow.com"))
        assertTrue(urlsByDomain.containsKey("example.com"))

        assertEquals(2, urlsByDomain["github.com"]?.size)
        assertEquals(2, urlsByDomain["stackoverflow.com"]?.size)
        assertEquals(1, urlsByDomain["example.com"]?.size)
    }

    @Test
    fun `extractUrls should handle empty or null input`() {
        val emptyUrls = HtmlUrlExtractor.extractUrls("")
        val nullUrls = HtmlUrlExtractor.extractUrls("")

        assertTrue(emptyUrls.isEmpty())
        assertTrue(nullUrls.isEmpty())
    }

    @Test
    fun `extractUrls should handle Google search result format`() {
        // Simulate a simplified Google search result HTML structure
        val googleHtml = """
            <div class="g">
                <h3><a href="https://www.example.com/page1">Example Page 1</a></h3>
                <div class="s">
                    <span class="st">Description of the page...</span>
                    <cite>https://www.example.com/page1</cite>
                </div>
            </div>
            <div class="g">
                <h3><a href="https://github.com/user/project">GitHub Project</a></h3>
                <div class="s">
                    <span class="st">A cool project on GitHub...</span>
                    <cite>https://github.com/user/project</cite>
                </div>
            </div>
        """.trimIndent()

        val urls = HtmlUrlExtractor.extractUrls(googleHtml)

        assertTrue(urls.contains("https://www.example.com/page1"))
        assertTrue(urls.contains("https://github.com/user/project"))
        // Should contain at least the 2 URLs from href attributes
        assertTrue("Should contain at least 2 URLs", urls.size >= 2)
    }
}
