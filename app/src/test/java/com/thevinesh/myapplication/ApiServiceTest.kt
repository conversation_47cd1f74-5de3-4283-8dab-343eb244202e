package com.thevinesh.myapplication

import com.thevinesh.myapplication.di.NetworkModule
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import org.junit.Assert.*
import org.junit.Test
import retrofit2.Retrofit
import retrofit2.converter.scalars.ScalarsConverterFactory
import retrofit2.http.GET
import retrofit2.http.Query
import retrofit2.Response
import java.util.concurrent.TimeUnit

class ApiServiceTest {

    // Test interface for relative URLs
    interface TestApiService {
        @GET("search")
        suspend fun getFirstPageOfGoogle(@Query("q") query: String): Response<String>
    }

    @Test
    fun `NetworkModule should provide working Retrofit instance`() {
        // Test that the NetworkModule provides a working Retrofit instance
        val networkModule = NetworkModule

        val loggingInterceptor = networkModule.provideHttpLoggingInterceptor()
        assertNotNull("Logging interceptor should not be null", loggingInterceptor)
        assertEquals("Logging level should be BODY", HttpLoggingInterceptor.Level.BODY, loggingInterceptor.level)

        val okHttpClient = networkModule.provideOkHttpClient(loggingInterceptor)
        assertNotNull("OkHttpClient should not be null", okHttpClient)
        assertEquals("Connect timeout should be 30 seconds", 30000, okHttpClient.connectTimeoutMillis)
        assertEquals("Read timeout should be 30 seconds", 30000, okHttpClient.readTimeoutMillis)
        assertEquals("Write timeout should be 30 seconds", 30000, okHttpClient.writeTimeoutMillis)

        val retrofit = networkModule.provideRetrofit(okHttpClient)
        assertNotNull("Retrofit should not be null", retrofit)
        assertEquals("Base URL should match", "https://jsonplaceholder.typicode.com/", retrofit.baseUrl().toString())

        // Verify that the retrofit instance has the scalar converter factory
        val converterFactories = retrofit.converterFactories()
        assertTrue("Should have at least one converter factory", converterFactories.size > 0)

        // Check if ScalarsConverterFactory is present
        val hasScalarConverter = converterFactories.any {
            it.javaClass.simpleName.contains("Scalars")
        }
        assertTrue("Should have ScalarsConverterFactory", hasScalarConverter)
    }

    @Test
    fun `Retrofit with ScalarsConverterFactory should handle String responses`() {
        // Create a test Retrofit instance with ScalarsConverterFactory
        val retrofit = Retrofit.Builder()
            .baseUrl("https://httpbin.org/")
            .addConverterFactory(ScalarsConverterFactory.create())
            .client(OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS)
                .readTimeout(10, TimeUnit.SECONDS)
                .build())
            .build()

        val testService = retrofit.create(TestApiService::class.java)
        assertNotNull("Test service should not be null", testService)

        // This test verifies that the service can be created without throwing an exception
        // The actual network call would require internet connectivity, so we just test the setup
    }

    @Test
    fun `ApiService should be creatable with proper Retrofit configuration`() {
        // Test that we can create the ApiService without errors
        val retrofit = Retrofit.Builder()
            .baseUrl("https://jsonplaceholder.typicode.com/")
            .addConverterFactory(ScalarsConverterFactory.create())
            .client(OkHttpClient.Builder().build())
            .build()

        val apiService = retrofit.create(com.thevinesh.myapplication.data.api.ApiService::class.java)
        assertNotNull("ApiService should not be null", apiService)

        // This test verifies that the ApiService can be created successfully with the ScalarsConverterFactory
        // which is the main fix for handling HTML responses instead of JSON
    }
}
