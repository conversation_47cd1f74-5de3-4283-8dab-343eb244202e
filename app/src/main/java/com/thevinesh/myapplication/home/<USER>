package com.thevinesh.myapplication.home

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel
import com.thevinesh.myapplication.ui.theme.LocalAiSearchTheme

@Composable
fun HomeContent(
    vm: HomeVm = hiltViewModel()
) {
    val query = vm.query.collectAsState()
    Scaffold(
        modifier = Modifier
            .fillMaxSize()
            .windowInsetsPadding(WindowInsets.statusBars), // Handle status bar insets
        bottomBar = {
            SearchBar(
                modifier = Modifier.windowInsetsPadding(WindowInsets.navigationBars), // Handle navigation bar insets
                query = query.value,
                onValueChanged = vm::onQueryChanged,
                onSearch = vm::onSearch
            )
        }
    ) { innerPadding ->
        Column(modifier = Modifier
            .fillMaxSize()
            .padding(innerPadding)) { }
    }
}

@Preview(showBackground = true)
@Composable
fun HomeContentPreview() {
    LocalAiSearchTheme {
        HomeContent()
    }
}