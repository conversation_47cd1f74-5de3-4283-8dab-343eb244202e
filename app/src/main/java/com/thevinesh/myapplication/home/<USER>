package com.thevinesh.myapplication.home

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.thevinesh.myapplication.data.repository.HomeRepository
import com.thevinesh.myapplication.utils.HtmlUrlExtractor
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class HomeVm @Inject constructor(
    private val homeRepository: HomeRepository
) : ViewModel() {
    private val _query = MutableStateFlow("")
    val query = _query.asStateFlow()

    fun onQueryChanged(value: String) {
        _query.value = value
    }

    fun onSearch(query: String) {
        viewModelScope.launch {
            try {
                val response = homeRepository.getFirstPageOfGoogle(query)

                if (response.isSuccessful) {
                    val htmlContent = response.body()
                    if (!htmlContent.isNullOrEmpty()) {
                        // Extract URLs from the HTML response
                        val extractedUrls = HtmlUrlExtractor.extractUrls(htmlContent)

                        Log.d("Vinesh", "Search query: '$query'")
                        Log.d("Vinesh", "Total URLs found: ${extractedUrls.size}")

                        if (extractedUrls.isNotEmpty()) {
                            Log.d("Vinesh", "Extracted webpage URLs:")
                            extractedUrls.forEachIndexed { index, url ->
                                Log.d("Vinesh", "${index + 1}. $url")
                            }

                            // Also log URLs grouped by domain for better organization
                            val urlsByDomain = HtmlUrlExtractor.extractUrlsByDomain(htmlContent)
                            Log.d("Vinesh", "\nURLs grouped by domain:")
                            urlsByDomain.forEach { (domain, urls) ->
                                Log.d("Vinesh", "$domain (${urls.size} URLs):")
                                urls.forEach { url ->
                                    Log.d("Vinesh", "  - $url")
                                }
                            }
                        } else {
                            Log.w("Vinesh", "No webpage URLs found in the response")
                        }
                    } else {
                        Log.e("Vinesh", "Response body is empty")
                    }
                } else {
                    Log.e("Vinesh", "Request failed with code: ${response.code()}")
                }
            } catch (e: Exception) {
                Log.e("Vinesh", "Error during search: ${e.message}", e)
            }
        }
    }
}