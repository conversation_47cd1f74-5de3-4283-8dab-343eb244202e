package com.thevinesh.myapplication.utils

import java.util.regex.Pattern

object HtmlUrlExtractor {
    
    /**
     * Extracts webpage URLs from HTML content.
     * This function looks for URLs in href attributes of anchor tags and other common patterns.
     */
    fun extractUrls(html: String): List<String> {
        val urls = mutableSetOf<String>()
        
        // Extract URLs from href attributes in anchor tags
        val hrefPattern = Pattern.compile(
            """href\s*=\s*["']([^"']+)["']""",
            Pattern.CASE_INSENSITIVE
        )
        
        val hrefMatcher = hrefPattern.matcher(html)
        while (hrefMatcher.find()) {
            val url = hrefMatcher.group(1)
            if (url != null && isValidWebUrl(url)) {
                urls.add(cleanUrl(url))
            }
        }
        
        // Extract URLs from data-href attributes (common in modern web apps)
        val dataHrefPattern = Pattern.compile(
            """data-href\s*=\s*["']([^"']+)["']""",
            Pattern.CASE_INSENSITIVE
        )
        
        val dataHrefMatcher = dataHrefPattern.matcher(html)
        while (dataHrefMatcher.find()) {
            val url = dataHrefMatcher.group(1)
            if (url != null && isValidWebUrl(url)) {
                urls.add(cleanUrl(url))
            }
        }
        
        // Extract URLs from ping attributes (used by Google for tracking)
        val pingPattern = Pattern.compile(
            """ping\s*=\s*["']([^"']+)["']""",
            Pattern.CASE_INSENSITIVE
        )
        
        val pingMatcher = pingPattern.matcher(html)
        while (pingMatcher.find()) {
            val url = pingMatcher.group(1)
            if (url != null && isValidWebUrl(url)) {
                urls.add(cleanUrl(url))
            }
        }
        
        // Extract direct HTTP/HTTPS URLs from text content
        val urlPattern = Pattern.compile(
            """https?://[^\s<>"']+""",
            Pattern.CASE_INSENSITIVE
        )
        
        val urlMatcher = urlPattern.matcher(html)
        while (urlMatcher.find()) {
            val url = urlMatcher.group()
            if (isValidWebUrl(url)) {
                urls.add(cleanUrl(url))
            }
        }
        
        return urls.toList().sorted()
    }
    
    /**
     * Checks if a URL is a valid web URL (not a relative path, javascript, mailto, etc.)
     */
    private fun isValidWebUrl(url: String): Boolean {
        val cleanedUrl = url.trim()
        
        // Must start with http or https
        if (!cleanedUrl.startsWith("http://") && !cleanedUrl.startsWith("https://")) {
            return false
        }
        
        // Filter out common non-webpage URLs
        val excludePatterns = listOf(
            "javascript:",
            "mailto:",
            "tel:",
            "ftp:",
            "file:",
            "#",
            "data:",
            "blob:"
        )
        
        for (pattern in excludePatterns) {
            if (cleanedUrl.startsWith(pattern, ignoreCase = true)) {
                return false
            }
        }
        
        // Filter out common file extensions that are not webpages
        val fileExtensions = listOf(
            ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx",
            ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".svg", ".webp",
            ".mp3", ".mp4", ".avi", ".mov", ".wmv", ".flv",
            ".zip", ".rar", ".tar", ".gz", ".7z",
            ".exe", ".dmg", ".pkg", ".deb", ".rpm"
        )
        
        val lowerUrl = cleanedUrl.lowercase()
        for (ext in fileExtensions) {
            if (lowerUrl.endsWith(ext)) {
                return false
            }
        }
        
        // Must contain a domain (at least one dot after http://)
        val domainPattern = Pattern.compile("""https?://[^/]+\.[^/]+""")
        if (!domainPattern.matcher(cleanedUrl).find()) {
            return false
        }
        
        return true
    }
    
    /**
     * Cleans and normalizes a URL
     */
    private fun cleanUrl(url: String): String {
        var cleaned = url.trim()
        
        // Remove trailing punctuation that might be part of HTML
        while (cleaned.isNotEmpty() && cleaned.last() in ".,;:!?\"')}]>") {
            cleaned = cleaned.dropLast(1)
        }
        
        // Remove URL fragments (everything after #) for cleaner URLs
        val fragmentIndex = cleaned.indexOf('#')
        if (fragmentIndex != -1) {
            cleaned = cleaned.substring(0, fragmentIndex)
        }
        
        return cleaned
    }
    
    /**
     * Extracts URLs and groups them by domain for better organization
     */
    fun extractUrlsByDomain(html: String): Map<String, List<String>> {
        val urls = extractUrls(html)
        return urls.groupBy { url ->
            try {
                val domain = url.substringAfter("://").substringBefore("/")
                domain.removePrefix("www.")
            } catch (e: Exception) {
                "unknown"
            }
        }
    }
}
